# Configurações para testes de NFSe Goiânia

# URL do webservice oficial (pode ser mockado em testes)
nfse.goiania.url=https://nfse.goiania.go.gov.br/ws/nfse.asmx

# Configurações de timeout reduzidas para testes
http.client.connect-timeout=10000
http.client.read-timeout=30000

# Configurações de logging para testes
logging.level.com.br.sasw.esocial_novo.service.NFSeGoianiaService=DEBUG
logging.level.com.br.sasw.esocial_novo.controller.NFSeGoianiaController=DEBUG
logging.level.com.br.sasw.esocial_novo.util.NFSeXmlSigner=DEBUG

# Configurações de certificado para testes (usar certificados de teste)
# certificado.teste.caminho=${CERTIFICADO_TESTE_PATH:/path/to/certificado-teste.p12}
# certificado.teste.senha=${CERTIFICADO_TESTE_SENHA:senha_teste}

# Desabilita SSL verification para testes (apenas em ambiente de teste)
trust.all.certificates=true

# Configurações do Spring Boot para testes
spring.main.banner-mode=off
logging.level.org.springframework.web=DEBUG
