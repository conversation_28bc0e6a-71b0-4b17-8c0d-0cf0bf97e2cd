package com.br.sasw.esocial_novo.controller;

import com.br.sasw.esocial_novo.util.NFSeGoianiaXmlSigner;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/teste")
public class TesteController {

    @PostMapping("/nfse-goiania")
    public String signNFSeGoiania(@RequestBody String xml) {
        return NFSeGoianiaXmlSigner.sign(xml, "FEDERAL");
    }

    @PostMapping("/nfse-goiania-check")
    public String checkNFSeGoiania(@RequestBody String xml) {
        boolean isAlreadySigned = NFSeGoianiaXmlSigner.isXmlSigned(xml);
        if (isAlreadySigned) {
            return "XML já está assinado:\n" + xml;
        } else {
            return "XML não assinado, assinando:\n" + NFSeGoianiaXmlSigner.sign(xml, "FEDERAL");
        }
    }
}
