package com.br.sasw.esocial_novo.controller;

import com.br.sasw.esocial_novo.util.XmlSigner;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/teste")
public class TesteController {

    @PostMapping
    public String getEscapedXml(@RequestBody String xml) {

        return XmlSigner.sign(xml, "FEDERAL");
    }
}
