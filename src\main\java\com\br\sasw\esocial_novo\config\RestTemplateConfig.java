package com.br.sasw.esocial_novo.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.ssl.SSLContextBuilder;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;

/**
 * Configuração do RestTemplate para comunicação HTTP
 */
@Configuration
public class RestTemplateConfig {

    /**
     * Bean do RestTemplate configurado para NFSe Goiânia
     */
    @Bean
    public RestTemplate restTemplate() {
        try {
            // Configuração SSL para aceitar certificados auto-assinados (se necessário)
            SSLContext sslContext = SSLContextBuilder
                    .create()
                    .loadTrustMaterial(new TrustSelfSignedStrategy())
                    .build();

            SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(sslContext);

            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLSocketFactory(socketFactory)
                    .build();

            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
            factory.setHttpClient(httpClient);
            factory.setConnectTimeout(30000); // 30 segundos
            factory.setReadTimeout(60000);    // 60 segundos

            return new RestTemplate(factory);

        } catch (NoSuchAlgorithmException | KeyStoreException | KeyManagementException e) {
            // Fallback para RestTemplate padrão se houver erro na configuração SSL
            RestTemplate restTemplate = new RestTemplate();
            
            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
            factory.setConnectTimeout(30000);
            factory.setReadTimeout(60000);
            restTemplate.setRequestFactory(factory);
            
            return restTemplate;
        }
    }
}
