package com.br.sasw.esocial_novo.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.ssl.SSLContextBuilder;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;

/**
 * Configuração do RestTemplate para comunicação HTTP/SOAP
 */
@Configuration
public class RestTemplateConfig {

    /**
     * Bean do RestTemplate configurado para comunicação SOAP com NFSe Goiânia
     */
    @Bean
    public RestTemplate restTemplate() {
        try {
            // Configuração SSL para comunicação segura com webservice oficial
            SSLContext sslContext = SSLContextBuilder
                    .create()
                    .loadTrustMaterial(new TrustSelfSignedStrategy())
                    .build();

            SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(
                sslContext,
                new String[]{"TLSv1.2", "TLSv1.3"},
                null,
                SSLConnectionSocketFactory.getDefaultHostnameVerifier()
            );

            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLSocketFactory(socketFactory)
                    .setUserAgent("NFSe-Goiania-Client/1.0")
                    .build();

            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
            factory.setHttpClient(httpClient);
            factory.setConnectTimeout(45000); // 45 segundos para webservice oficial
            factory.setReadTimeout(120000);   // 2 minutos para processamento

            return new RestTemplate(factory);

        } catch (NoSuchAlgorithmException | KeyStoreException | KeyManagementException e) {
            // Fallback para RestTemplate padrão se houver erro na configuração SSL
            RestTemplate restTemplate = new RestTemplate();

            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
            factory.setConnectTimeout(45000);
            factory.setReadTimeout(120000);
            restTemplate.setRequestFactory(factory);

            // Adiciona interceptor para logging em caso de fallback
            restTemplate.getInterceptors().add((request, body, execution) -> {
                System.out.println("FALLBACK SSL - Enviando para: " + request.getURI());
                return execution.execute(request, body);
            });

            return restTemplate;
        }
    }
}
