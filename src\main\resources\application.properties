server.port=8081
spring.application.name=esocial-novo
esocial.certificado.path=certificado.pfx
esocial.certificado.senha=1234

esocial.wsdl.context-path=com.esocial.wsdl

esocial-envio.service.url=https://webservices.envio.esocial.gov.br/servicos/empregador/enviarloteeventos/WsEnviarLoteEventos.svc
esocial-envio.service.action.enviarLoteEventos=http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0/ServicoEnviarLoteEventos/EnviarLoteEventos

logging.level.org.springframework.ws.client.MessageTracing=DEBUG
logging.level.org.apache.http.wire=DEBUG
javax.net.debug=ssl

spring.datasource.url=***************************************************************************************************
spring.datasource.username=sasw
spring.datasource.password=s@$26bd1
spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.hikari.connection-timeout=20000
logging.level.org.springframework.jdbc=DEBUG
