package com.br.sasw.esocial_novo.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.crypto.dsig.*;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.StringWriter;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Collections;

/**
 * Assinador específico para XMLs de NFSe Goiânia
 * Coloca a assinatura no local correto conforme o XSD
 */
@UtilityClass
@Slf4j
public class NFSeGoianiaXmlSigner {

    /**
     * Assina um XML de NFSe Goiânia colocando a assinatura no local correto
     * 
     * @param xml XML a ser assinado
     * @param empresa Identificador da empresa para buscar o certificado
     * @return XML assinado digitalmente
     */
    public String sign(String xml, String empresa) {
        try {
            log.debug("Iniciando assinatura digital do XML NFSe Goiânia para empresa: {}", empresa);

            // Busca o certificado da empresa
            Certificado certificate = CarregarCertificados.buscaCertificadoPorChaveEmpresa(empresa);
            String caminhoCertificado = certificate.getLocal();
            String senhaCertificado = certificate.getSenha();

            // Carrega o keystore
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(new FileInputStream(caminhoCertificado), senhaCertificado.toCharArray());

            // Busca o alias do certificado
            String alias = null;
            for (var e = keyStore.aliases(); e.hasMoreElements(); ) {
                alias = e.nextElement();
                if (keyStore.isKeyEntry(alias)) {
                    break;
                }
            }

            if (alias == null) {
                throw new RuntimeException("Nenhuma chave privada encontrada no certificado");
            }

            // Obtém a chave privada e o certificado
            PrivateKey chavePrivada = (PrivateKey) keyStore.getKey(alias, senhaCertificado.toCharArray());
            X509Certificate certificado = (X509Certificate) keyStore.getCertificate(alias);

            log.debug("Certificado carregado com sucesso. Subject: {}", certificado.getSubjectDN());

            // Carrega e processa o XML
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            Document doc = dbf.newDocumentBuilder().parse(
                new InputSource(new ByteArrayInputStream(xml.getBytes("utf-8")))
            );

            // Busca o elemento InfDeclaracaoPrestacaoServico para assinar
            NodeList infDeclaracaoList = doc.getElementsByTagName("InfDeclaracaoPrestacaoServico");
            if (infDeclaracaoList.getLength() == 0) {
                throw new RuntimeException("Elemento InfDeclaracaoPrestacaoServico não encontrado no XML");
            }

            return signNFSeDocument(doc, chavePrivada, certificado, "");

        } catch (Exception e) {
            log.error("Erro ao assinar XML de NFSe Goiânia", e);
            throw new RuntimeException("Erro ao assinar XML de NFSe Goiânia: " + e.getMessage(), e);
        }
    }

    /**
     * Realiza a assinatura do documento NFSe
     */
    private String signNFSeDocument(Document doc, PrivateKey chavePrivada, X509Certificate certificado, String reference) 
            throws Exception {
        
        // Cria a fábrica de assinatura
        XMLSignatureFactory sigFactory = XMLSignatureFactory.getInstance("DOM");

        // Cria a referência para assinatura
        Reference ref;
        if (reference == null || reference.isEmpty()) {
            // Assina o documento inteiro
            ref = sigFactory.newReference(
                "",
                sigFactory.newDigestMethod(DigestMethod.SHA256, null),
                Arrays.asList(
                    sigFactory.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null),
                    sigFactory.newTransform(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null)
                ),
                null,
                null
            );
        } else {
            // Assina elemento específico
            ref = sigFactory.newReference(
                reference,
                sigFactory.newDigestMethod(DigestMethod.SHA256, null),
                Arrays.asList(
                    sigFactory.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null),
                    sigFactory.newTransform(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null)
                ),
                null,
                null
            );
        }

        // Define as informações da assinatura
        SignedInfo signedInfo = sigFactory.newSignedInfo(
            sigFactory.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null),
            sigFactory.newSignatureMethod(SignatureMethod.RSA_SHA256, null),
            Collections.singletonList(ref)
        );

        // Cria a KeyInfo com o certificado
        KeyInfoFactory kif = sigFactory.getKeyInfoFactory();
        X509Data x509Data = kif.newX509Data(Collections.singletonList(certificado));
        KeyInfo keyInfo = kif.newKeyInfo(Collections.singletonList(x509Data));

        // Busca o elemento Rps onde deve ser inserida a assinatura
        NodeList rpsList = doc.getElementsByTagName("Rps");
        if (rpsList.getLength() == 0) {
            throw new RuntimeException("Elemento Rps não encontrado para inserir a assinatura");
        }
        
        Element rpsElement = (Element) rpsList.item(0);
        
        // Define o contexto da assinatura - a assinatura será inserida no final do elemento Rps
        DOMSignContext dsc = new DOMSignContext(chavePrivada, rpsElement);

        // Cria e aplica a assinatura
        XMLSignature signature = sigFactory.newXMLSignature(signedInfo, keyInfo);
        signature.sign(dsc);

        // Converte o documento assinado para string
        Transformer transformer = TransformerFactory.newInstance().newTransformer();
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "no");
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.ENCODING, "UTF-8");
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.METHOD, "xml");
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.OMIT_XML_DECLARATION, "yes");

        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(doc), new StreamResult(writer));
        
        String result = writer.toString();
        log.debug("XML NFSe Goiânia assinado com sucesso");
        
        return result;
    }

    /**
     * Verifica se um XML já está assinado
     */
    public boolean isXmlSigned(String xml) {
        try {
            return xml.contains("<Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\">");
        } catch (Exception e) {
            log.warn("Erro ao verificar se XML está assinado", e);
            return false;
        }
    }
}
