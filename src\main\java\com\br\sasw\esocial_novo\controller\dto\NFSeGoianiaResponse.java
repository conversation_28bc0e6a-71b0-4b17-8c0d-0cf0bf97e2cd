package com.br.sasw.esocial_novo.controller.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * DTO para resposta das operações de NFSe Goiânia
 */
@Data
public class NFSeGoianiaResponse {
    
    /**
     * XML de retorno do webservice
     */
    private String xmlRetorno;
    
    /**
     * Data e hora do processamento
     */
    private LocalDateTime dataHoraProcessamento;
    
    /**
     * Número da NFSe gerada (se sucesso)
     */
    private String numeroNfse;
    
    /**
     * Código de verificação da NFSe (se sucesso)
     */
    private String codigoVerificacao;
    
    /**
     * Indica se a operação foi bem-sucedida
     */
    private boolean sucesso;
    
    /**
     * Mensagem de erro (se houver)
     */
    private String mensagemErro;
    
    /**
     * XML enviado para o webservice
     */
    private String xmlEnviado;
    
    /**
     * Status da operação
     */
    private String status;
}
