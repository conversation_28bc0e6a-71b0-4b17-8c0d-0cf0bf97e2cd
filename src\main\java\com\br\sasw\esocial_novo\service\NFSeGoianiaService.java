package com.br.sasw.esocial_novo.service;

import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaConsultaRequest;
import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaEnvioRequest;
import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaResponse;
import com.br.sasw.esocial_novo.exception.NFSeGoianiaException;
import com.br.sasw.esocial_novo.util.NFSeXmlSigner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.time.LocalDateTime;

/**
 * Service para operações de NFSe Goiânia
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NFSeGoianiaService {

    private final RestTemplate restTemplate;

    @Value("${nfse.goiania.url:https://nfse.goiania.go.gov.br/ws/nfse.asmx}")
    private String nfseGoianiaUrl;

    /**
     * Envia uma NFSe para o webservice de Goiânia
     * Fluxo: Recebe XML -> Assina -> Envia -> Processa retorno
     */
    public NFSeGoianiaResponse enviarNfse(NFSeGoianiaEnvioRequest request) {
        log.info("Iniciando envio de NFSe - CNPJ: {}, Praça: {}, Número: {}",
                request.getCnpj(), request.getPraca(), request.getNumero());

        NFSeGoianiaResponse response = new NFSeGoianiaResponse();
        response.setDataHoraProcessamento(LocalDateTime.now());

        try {
            // 1. Verifica se o XML já está assinado
            String xmlParaEnvio = request.getXml();
            if (!NFSeXmlSigner.isXmlSigned(xmlParaEnvio)) {
                log.debug("Assinando XML digitalmente para CNPJ: {}", request.getCnpj());
                xmlParaEnvio = NFSeXmlSigner.sign(xmlParaEnvio, request.getCnpj());
            } else {
                log.debug("XML já está assinado, prosseguindo com envio");
            }
            response.setXmlEnviado(xmlParaEnvio);

            // 2. Envia para o webservice oficial de Goiânia
            log.debug("Enviando XML para webservice oficial de Goiânia");
            String xmlRetorno = chamarWebserviceGerar(xmlParaEnvio);
            response.setXmlRetorno(xmlRetorno);

            // 3. Processa o retorno
            processarRetornoEnvio(response, xmlRetorno, request);

            log.info("Envio de NFSe concluído com sucesso - CNPJ: {}, Status: {}",
                    request.getCnpj(), response.getStatus());

        } catch (Exception e) {
            log.error("Erro ao enviar NFSe - CNPJ: {}, Praça: {}, Número: {}",
                    request.getCnpj(), request.getPraca(), request.getNumero(), e);

            throw new NFSeGoianiaException("ENVIO_ERROR",
                    "Erro ao enviar NFSe",
                    "Problema ao enviar documento: " + e.getMessage(), e);
        }

        return response;
    }

    /**
     * Consulta uma NFSe no webservice de Goiânia
     */
    public NFSeGoianiaResponse consultarNfse(NFSeGoianiaConsultaRequest request) {
        log.info("Iniciando consulta de NFSe - CNPJ: {}, Praça: {}, Número: {}", 
                request.getCnpj(), request.getPraca(), request.getNumero());

        NFSeGoianiaResponse response = new NFSeGoianiaResponse();
        response.setDataHoraProcessamento(LocalDateTime.now());
        response.setXmlEnviado(request.getXml());

        try {
            // Chama o webservice de consulta
            String xmlRetorno = chamarWebserviceConsultar(request.getXml());
            response.setXmlRetorno(xmlRetorno);

            // Processa o retorno
            processarRetornoConsulta(response, xmlRetorno, request);

        } catch (Exception e) {
            log.error("Erro ao consultar NFSe - CNPJ: {}, Praça: {}, Número: {}",
                    request.getCnpj(), request.getPraca(), request.getNumero(), e);

            throw new NFSeGoianiaException("CONSULTA_ERROR",
                    "Erro ao consultar NFSe",
                    "Problema ao consultar documento: " + e.getMessage(), e);
        }

        return response;
    }

    /**
     * Chama o webservice oficial de Goiânia para gerar NFSe
     */
    private String chamarWebserviceGerar(String xml) {
        log.debug("Chamando webservice oficial de Goiânia: {}", nfseGoianiaUrl);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_XML);
        headers.set("SOAPAction", "http://nfse.goiania.go.gov.br/ws/GerarNfse");
        headers.set("charset", "utf-8");

        String soapEnvelope = buildSoapEnvelope(xml, "GerarNfse");
        HttpEntity<String> entity = new HttpEntity<>(soapEnvelope, headers);

        log.debug("Enviando requisição SOAP para: {}", nfseGoianiaUrl);
        ResponseEntity<String> response = restTemplate.exchange(
                nfseGoianiaUrl,
                HttpMethod.POST,
                entity,
                String.class
        );

        log.debug("Resposta recebida do webservice oficial");
        return extractSoapResponse(response.getBody());
    }

    /**
     * Chama o webservice oficial de Goiânia para consultar NFSe
     */
    private String chamarWebserviceConsultar(String xml) {
        log.debug("Chamando webservice oficial de Goiânia para consulta: {}", nfseGoianiaUrl);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_XML);
        headers.set("SOAPAction", "http://nfse.goiania.go.gov.br/ws/ConsultarNfseRps");
        headers.set("charset", "utf-8");

        String soapEnvelope = buildSoapEnvelope(xml, "ConsultarNfseRps");
        HttpEntity<String> entity = new HttpEntity<>(soapEnvelope, headers);

        log.debug("Enviando requisição SOAP de consulta para: {}", nfseGoianiaUrl);
        ResponseEntity<String> response = restTemplate.exchange(
                nfseGoianiaUrl,
                HttpMethod.POST,
                entity,
                String.class
        );

        log.debug("Resposta de consulta recebida do webservice oficial");
        return extractSoapResponse(response.getBody());
    }

    /**
     * Constrói o envelope SOAP para comunicação direta com webservice oficial
     */
    private String buildSoapEnvelope(String xml, String operation) {
        // Escapa o XML para inserção no SOAP
        String xmlEscapado = xml.replace("&", "&amp;")
                                .replace("<", "&lt;")
                                .replace(">", "&gt;")
                                .replace("\"", "&quot;")
                                .replace("'", "&apos;");

        return String.format("""
                <?xml version="1.0" encoding="utf-8"?>
                <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                    <soap:Body>
                        <%s xmlns="http://nfse.goiania.go.gov.br/ws/">
                            <ArquivoXML>%s</ArquivoXML>
                        </%s>
                    </soap:Body>
                </soap:Envelope>
                """, operation, xmlEscapado, operation);
    }

    /**
     * Extrai a resposta do envelope SOAP do webservice oficial
     */
    private String extractSoapResponse(String soapResponse) {
        try {
            log.debug("Extraindo resposta do envelope SOAP");

            // Busca pelo resultado de geração
            int startIndex = soapResponse.indexOf("<GerarNfseResult>");
            String endTag = "</GerarNfseResult>";

            // Se não encontrou, busca pelo resultado de consulta
            if (startIndex == -1) {
                startIndex = soapResponse.indexOf("<ConsultarNfseRpsResult>");
                endTag = "</ConsultarNfseRpsResult>";
            }

            if (startIndex != -1) {
                int endIndex = soapResponse.indexOf(endTag, startIndex);
                if (endIndex != -1) {
                    String result = soapResponse.substring(
                        startIndex + soapResponse.substring(startIndex).indexOf(">") + 1,
                        endIndex
                    );

                    // Decodifica entidades XML
                    result = result.replace("&lt;", "<")
                                  .replace("&gt;", ">")
                                  .replace("&quot;", "\"")
                                  .replace("&apos;", "'")
                                  .replace("&amp;", "&");

                    log.debug("Resposta extraída com sucesso");
                    return result;
                }
            }

            log.warn("Não foi possível extrair resposta do SOAP, retornando resposta completa");
            return soapResponse;

        } catch (Exception e) {
            log.error("Erro ao extrair resposta SOAP", e);
            return soapResponse;
        }
    }

    /**
     * Processa o retorno do envio de NFSe
     */
    private void processarRetornoEnvio(NFSeGoianiaResponse response, String xmlRetorno, NFSeGoianiaEnvioRequest request) {
        try {
            if (xmlRetorno.contains("<ListaNfse><CompNfse><Nfse><InfNfse><Numero>")) {
                // NFSe gerada com sucesso
                DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
                DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
                Document doc = dBuilder.parse(new InputSource(new StringReader(xmlRetorno)));
                
                NodeList nList = doc.getElementsByTagName("GerarNfseResposta");
                if (nList.getLength() > 0) {
                    Node nNode = nList.item(0);
                    if (nNode.getNodeType() == Node.ELEMENT_NODE) {
                        Element eElement = (Element) nNode;
                        
                        NodeList numeroNodes = eElement.getElementsByTagName("Numero");
                        if (numeroNodes.getLength() > 0) {
                            response.setNumeroNfse(numeroNodes.item(0).getTextContent());
                        }
                        
                        NodeList codigoNodes = eElement.getElementsByTagName("CodigoVerificacao");
                        if (codigoNodes.getLength() > 0) {
                            response.setCodigoVerificacao(codigoNodes.item(0).getTextContent());
                        }
                    }
                }
                
                response.setSucesso(true);
                response.setStatus("SUCESSO");
                
            } else {
                // Erro na geração
                response.setSucesso(false);
                response.setStatus("ERRO");
                response.setMensagemErro(String.format(
                        "Nota não gerada - Praça: %d, Número: %s, Série: %s - %s",
                        request.getPraca(), request.getNumero(), request.getSerie(), xmlRetorno
                ));
            }
            
        } catch (Exception e) {
            log.error("Erro ao processar retorno do envio", e);
            response.setSucesso(false);
            response.setStatus("ERRO");
            response.setMensagemErro("Erro ao processar retorno: " + e.getMessage());
        }
    }

    /**
     * Processa o retorno da consulta de NFSe
     */
    private void processarRetornoConsulta(NFSeGoianiaResponse response, String xmlRetorno, NFSeGoianiaConsultaRequest request) {
        try {
            // Lógica similar ao envio, adaptada para consulta
            response.setSucesso(true);
            response.setStatus("CONSULTADO");
            
        } catch (Exception e) {
            log.error("Erro ao processar retorno da consulta", e);
            response.setSucesso(false);
            response.setStatus("ERRO");
            response.setMensagemErro("Erro ao processar retorno da consulta: " + e.getMessage());
        }
    }
}
