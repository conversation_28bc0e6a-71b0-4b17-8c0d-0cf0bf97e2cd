package com.br.sasw.esocial_novo.service;

import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaConsultaRequest;
import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaEnvioRequest;
import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaResponse;
import com.br.sasw.esocial_novo.exception.NFSeGoianiaException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.time.LocalDateTime;

/**
 * Service para operações de NFSe Goiânia
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NFSeGoianiaService {

    private final RestTemplate restTemplate;

    @Value("${nfse.goiania.url:http://localhost:8080/webservice_nfse_goiania}")
    private String nfseGoianiaUrl;

    /**
     * Envia uma NFSe para o webservice de Goiânia
     */
    public NFSeGoianiaResponse enviarNfse(NFSeGoianiaEnvioRequest request) {
        log.info("Iniciando envio de NFSe - CNPJ: {}, Praça: {}, Número: {}", 
                request.getCnpj(), request.getPraca(), request.getNumero());

        NFSeGoianiaResponse response = new NFSeGoianiaResponse();
        response.setDataHoraProcessamento(LocalDateTime.now());
        response.setXmlEnviado(request.getXml());

        try {
            // Chama o webservice de geração
            String xmlRetorno = chamarWebserviceGerar(request.getXml());
            response.setXmlRetorno(xmlRetorno);

            // Processa o retorno
            processarRetornoEnvio(response, xmlRetorno, request);

        } catch (Exception e) {
            log.error("Erro ao enviar NFSe - CNPJ: {}, Praça: {}, Número: {}",
                    request.getCnpj(), request.getPraca(), request.getNumero(), e);

            throw new NFSeGoianiaException("ENVIO_ERROR",
                    "Erro ao enviar NFSe",
                    "Problema ao enviar documento: " + e.getMessage(), e);
        }

        return response;
    }

    /**
     * Consulta uma NFSe no webservice de Goiânia
     */
    public NFSeGoianiaResponse consultarNfse(NFSeGoianiaConsultaRequest request) {
        log.info("Iniciando consulta de NFSe - CNPJ: {}, Praça: {}, Número: {}", 
                request.getCnpj(), request.getPraca(), request.getNumero());

        NFSeGoianiaResponse response = new NFSeGoianiaResponse();
        response.setDataHoraProcessamento(LocalDateTime.now());
        response.setXmlEnviado(request.getXml());

        try {
            // Chama o webservice de consulta
            String xmlRetorno = chamarWebserviceConsultar(request.getXml());
            response.setXmlRetorno(xmlRetorno);

            // Processa o retorno
            processarRetornoConsulta(response, xmlRetorno, request);

        } catch (Exception e) {
            log.error("Erro ao consultar NFSe - CNPJ: {}, Praça: {}, Número: {}",
                    request.getCnpj(), request.getPraca(), request.getNumero(), e);

            throw new NFSeGoianiaException("CONSULTA_ERROR",
                    "Erro ao consultar NFSe",
                    "Problema ao consultar documento: " + e.getMessage(), e);
        }

        return response;
    }

    /**
     * Chama o webservice para gerar NFSe
     */
    private String chamarWebserviceGerar(String xml) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_XML);
        headers.set("SOAPAction", "http://nfse.goiania.go.gov.br/ws/GerarNfse");

        String soapEnvelope = buildSoapEnvelope(xml, "GerarNfse");
        HttpEntity<String> entity = new HttpEntity<>(soapEnvelope, headers);

        ResponseEntity<String> response = restTemplate.exchange(
                nfseGoianiaUrl + "/NFSe.asmx",
                HttpMethod.POST,
                entity,
                String.class
        );

        return extractSoapResponse(response.getBody());
    }

    /**
     * Chama o webservice para consultar NFSe
     */
    private String chamarWebserviceConsultar(String xml) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_XML);
        headers.set("SOAPAction", "http://nfse.goiania.go.gov.br/ws/ConsultarNfseRps");

        String soapEnvelope = buildSoapEnvelope(xml, "ConsultarNfseRps");
        HttpEntity<String> entity = new HttpEntity<>(soapEnvelope, headers);

        ResponseEntity<String> response = restTemplate.exchange(
                nfseGoianiaUrl + "/NFSe.asmx",
                HttpMethod.POST,
                entity,
                String.class
        );

        return extractSoapResponse(response.getBody());
    }

    /**
     * Constrói o envelope SOAP
     */
    private String buildSoapEnvelope(String xml, String operation) {
        return String.format("""
                <?xml version="1.0" encoding="utf-8"?>
                <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                               xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
                               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                    <soap:Body>
                        <%s xmlns="http://nfse.goiania.go.gov.br/ws/">
                            <ArquivoXML>%s</ArquivoXML>
                        </%s>
                    </soap:Body>
                </soap:Envelope>
                """, operation, xml, operation);
    }

    /**
     * Extrai a resposta do envelope SOAP
     */
    private String extractSoapResponse(String soapResponse) {
        // Implementação simplificada - em produção, usar parser XML adequado
        int startIndex = soapResponse.indexOf("<GerarNfseResult>");
        if (startIndex == -1) {
            startIndex = soapResponse.indexOf("<ConsultarNfseRpsResult>");
        }
        
        if (startIndex != -1) {
            int endIndex = soapResponse.indexOf("</", startIndex);
            if (endIndex != -1) {
                return soapResponse.substring(startIndex + soapResponse.substring(startIndex).indexOf(">") + 1, endIndex);
            }
        }
        
        return soapResponse;
    }

    /**
     * Processa o retorno do envio de NFSe
     */
    private void processarRetornoEnvio(NFSeGoianiaResponse response, String xmlRetorno, NFSeGoianiaEnvioRequest request) {
        try {
            if (xmlRetorno.contains("<ListaNfse><CompNfse><Nfse><InfNfse><Numero>")) {
                // NFSe gerada com sucesso
                DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
                DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
                Document doc = dBuilder.parse(new InputSource(new StringReader(xmlRetorno)));
                
                NodeList nList = doc.getElementsByTagName("GerarNfseResposta");
                if (nList.getLength() > 0) {
                    Node nNode = nList.item(0);
                    if (nNode.getNodeType() == Node.ELEMENT_NODE) {
                        Element eElement = (Element) nNode;
                        
                        NodeList numeroNodes = eElement.getElementsByTagName("Numero");
                        if (numeroNodes.getLength() > 0) {
                            response.setNumeroNfse(numeroNodes.item(0).getTextContent());
                        }
                        
                        NodeList codigoNodes = eElement.getElementsByTagName("CodigoVerificacao");
                        if (codigoNodes.getLength() > 0) {
                            response.setCodigoVerificacao(codigoNodes.item(0).getTextContent());
                        }
                    }
                }
                
                response.setSucesso(true);
                response.setStatus("SUCESSO");
                
            } else {
                // Erro na geração
                response.setSucesso(false);
                response.setStatus("ERRO");
                response.setMensagemErro(String.format(
                        "Nota não gerada - Praça: %d, Número: %s, Série: %s - %s",
                        request.getPraca(), request.getNumero(), request.getSerie(), xmlRetorno
                ));
            }
            
        } catch (Exception e) {
            log.error("Erro ao processar retorno do envio", e);
            response.setSucesso(false);
            response.setStatus("ERRO");
            response.setMensagemErro("Erro ao processar retorno: " + e.getMessage());
        }
    }

    /**
     * Processa o retorno da consulta de NFSe
     */
    private void processarRetornoConsulta(NFSeGoianiaResponse response, String xmlRetorno, NFSeGoianiaConsultaRequest request) {
        try {
            // Lógica similar ao envio, adaptada para consulta
            response.setSucesso(true);
            response.setStatus("CONSULTADO");
            
        } catch (Exception e) {
            log.error("Erro ao processar retorno da consulta", e);
            response.setSucesso(false);
            response.setStatus("ERRO");
            response.setMensagemErro("Erro ao processar retorno da consulta: " + e.getMessage());
        }
    }
}
