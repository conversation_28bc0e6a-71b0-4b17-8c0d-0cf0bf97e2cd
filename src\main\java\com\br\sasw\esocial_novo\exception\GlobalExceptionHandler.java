package com.br.sasw.esocial_novo.exception;

import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientException;

import java.time.LocalDateTime;

/**
 * Handler global para tratamento de exceções
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * Trata exceções específicas de NFSe Goiânia
     */
    @ExceptionHandler(NFSeGoianiaException.class)
    public ResponseEntity<NFSeGoianiaResponse> handleNFSeGoianiaException(NFSeGoianiaException ex) {
        log.error("Erro específico de NFSe Goiânia: {} - {}", ex.getCodigo(), ex.getMessage(), ex);

        NFSeGoianiaResponse response = new NFSeGoianiaResponse();
        response.setSucesso(false);
        response.setStatus("ERRO");
        response.setMensagemErro(ex.getMessage());
        response.setDataHoraProcessamento(LocalDateTime.now());

        return ResponseEntity.unprocessableEntity().body(response);
    }

    /**
     * Trata erros de conectividade com webservices
     */
    @ExceptionHandler(ResourceAccessException.class)
    public ResponseEntity<NFSeGoianiaResponse> handleResourceAccessException(ResourceAccessException ex) {
        log.error("Erro de conectividade com webservice: {}", ex.getMessage(), ex);

        NFSeGoianiaResponse response = new NFSeGoianiaResponse();
        response.setSucesso(false);
        response.setStatus("ERRO_CONECTIVIDADE");
        response.setMensagemErro("Erro de conectividade com o webservice de NFSe Goiânia. Tente novamente mais tarde.");
        response.setDataHoraProcessamento(LocalDateTime.now());

        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

    /**
     * Trata erros gerais de cliente REST
     */
    @ExceptionHandler(RestClientException.class)
    public ResponseEntity<NFSeGoianiaResponse> handleRestClientException(RestClientException ex) {
        log.error("Erro de cliente REST: {}", ex.getMessage(), ex);

        NFSeGoianiaResponse response = new NFSeGoianiaResponse();
        response.setSucesso(false);
        response.setStatus("ERRO_WEBSERVICE");
        response.setMensagemErro("Erro na comunicação com o webservice de NFSe Goiânia: " + ex.getMessage());
        response.setDataHoraProcessamento(LocalDateTime.now());

        return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(response);
    }

    /**
     * Trata exceções gerais não capturadas
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<NFSeGoianiaResponse> handleGenericException(Exception ex) {
        log.error("Erro inesperado: {}", ex.getMessage(), ex);

        NFSeGoianiaResponse response = new NFSeGoianiaResponse();
        response.setSucesso(false);
        response.setStatus("ERRO_INTERNO");
        response.setMensagemErro("Erro interno do servidor. Contate o suporte técnico.");
        response.setDataHoraProcessamento(LocalDateTime.now());

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
