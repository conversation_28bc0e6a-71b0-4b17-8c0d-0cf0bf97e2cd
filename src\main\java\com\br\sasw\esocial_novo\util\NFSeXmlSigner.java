package com.br.sasw.esocial_novo.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.crypto.dsig.*;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.StringWriter;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Collections;

/**
 * Utilitário para assinatura digital de XMLs de NFSe Goiânia
 */
@UtilityClass
@Slf4j
public class NFSeXmlSigner {

    /**
     * Assina um XML de NFSe para Goiânia
     * 
     * @param xml XML a ser assinado
     * @param empresa Identificador da empresa para buscar o certificado
     * @return XML assinado digitalmente
     */
    public String sign(String xml, String empresa) {
        try {
            log.debug("Iniciando assinatura digital do XML para empresa: {}", empresa);

            // Busca o certificado da empresa
            Certificado certificate = CarregarCertificados.buscaCertificadoPorChaveEmpresa(empresa);
            String caminhoCertificado = certificate.getLocal();
            String senhaCertificado = certificate.getSenha();

            // Carrega o keystore
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(new FileInputStream(caminhoCertificado), senhaCertificado.toCharArray());

            // Busca o alias do certificado
            String alias = null;
            for (var e = keyStore.aliases(); e.hasMoreElements(); ) {
                alias = e.nextElement();
                if (keyStore.isKeyEntry(alias)) {
                    break;
                }
            }

            if (alias == null) {
                throw new RuntimeException("Nenhuma chave privada encontrada no certificado");
            }

            // Obtém a chave privada e o certificado
            PrivateKey chavePrivada = (PrivateKey) keyStore.getKey(alias, senhaCertificado.toCharArray());
            X509Certificate certificado = (X509Certificate) keyStore.getCertificate(alias);

            log.debug("Certificado carregado com sucesso. Subject: {}", certificado.getSubjectDN());

            // Carrega e processa o XML
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            Document doc = dbf.newDocumentBuilder().parse(
                new InputSource(new ByteArrayInputStream(xml.getBytes("utf-8")))
            );

            // Busca o elemento a ser assinado (InfRps)
            NodeList infRpsList = doc.getElementsByTagName("InfRps");
            if (infRpsList.getLength() == 0) {
                log.warn("Elemento InfRps não encontrado, tentando assinar o documento raiz");
                return signDocument(doc, chavePrivada, certificado, "");
            }

            Element infRps = (Element) infRpsList.item(0);
            String id = infRps.getAttribute("Id");
            
            if (id == null || id.isEmpty()) {
                // Se não tem ID, gera um
                id = "InfRps1";
                infRps.setAttribute("Id", id);
                log.debug("ID gerado para InfRps: {}", id);
            }

            log.debug("Assinando elemento InfRps com ID: {}", id);
            return signDocument(doc, chavePrivada, certificado, "#" + id);

        } catch (Exception e) {
            log.error("Erro ao assinar XML de NFSe", e);
            throw new RuntimeException("Erro ao assinar XML de NFSe: " + e.getMessage(), e);
        }
    }

    /**
     * Realiza a assinatura do documento
     */
    private String signDocument(Document doc, PrivateKey chavePrivada, X509Certificate certificado, String reference) 
            throws Exception {
        
        // Cria a fábrica de assinatura
        XMLSignatureFactory sigFactory = XMLSignatureFactory.getInstance("DOM");

        // Cria a referência para assinatura
        Reference ref = sigFactory.newReference(
            reference,
            sigFactory.newDigestMethod(DigestMethod.SHA1, null),
            Arrays.asList(
                sigFactory.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null),
                sigFactory.newTransform(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null)
            ),
            null,
            null
        );

        // Define as informações da assinatura
        SignedInfo signedInfo = sigFactory.newSignedInfo(
            sigFactory.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null),
            sigFactory.newSignatureMethod(SignatureMethod.RSA_SHA1, null),
            Collections.singletonList(ref)
        );

        // Cria a KeyInfo com o certificado
        KeyInfoFactory kif = sigFactory.getKeyInfoFactory();
        X509Data x509Data = kif.newX509Data(Collections.singletonList(certificado));
        KeyInfo keyInfo = kif.newKeyInfo(Collections.singletonList(x509Data));

        // Define o contexto da assinatura
        Element elementToSign = doc.getDocumentElement();
        if (!reference.isEmpty()) {
            // Se tem referência específica, busca o elemento
            NodeList nodes = doc.getElementsByTagName("InfRps");
            if (nodes.getLength() > 0) {
                elementToSign = (Element) nodes.item(0).getParentNode();
            }
        }

        DOMSignContext dsc = new DOMSignContext(chavePrivada, elementToSign);

        // Cria e aplica a assinatura
        XMLSignature signature = sigFactory.newXMLSignature(signedInfo, keyInfo);
        signature.sign(dsc);

        // Converte o documento assinado para string
        Transformer transformer = TransformerFactory.newInstance().newTransformer();
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "no");
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.ENCODING, "UTF-8");
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.METHOD, "xml");
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.OMIT_XML_DECLARATION, "yes");

        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(doc), new StreamResult(writer));
        
        String result = writer.toString();
        log.debug("XML assinado com sucesso");
        
        return result;
    }

    /**
     * Verifica se um XML já está assinado
     */
    public boolean isXmlSigned(String xml) {
        try {
            return xml.contains("<Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\">");
        } catch (Exception e) {
            log.warn("Erro ao verificar se XML está assinado", e);
            return false;
        }
    }
}
