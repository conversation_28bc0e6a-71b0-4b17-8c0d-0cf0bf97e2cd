package com.br.sasw.esocial_novo.integration;

import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaEnvioRequest;
import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Testes de integração para NFSe Goiânia
 * 
 * Para executar testes com webservice real, configure:
 * NFSE_INTEGRATION_TEST=true
 * CERTIFICADO_TESTE_PATH=/path/to/certificado-teste.p12
 * CERTIFICADO_TESTE_SENHA=senha
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
class NFSeGoianiaIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testHealthCheck() throws Exception {
        mockMvc.perform(get("/api/nfse-goiania/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("NFSe Goiânia Service is running"));
    }

    @Test
    void testEnviarNfse_ValidacaoRequest() throws Exception {
        NFSeGoianiaEnvioRequest request = new NFSeGoianiaEnvioRequest();
        // Request inválido - sem XML
        request.setCnpj("12345678000123");
        request.setPraca(1);
        request.setNumero("1");

        mockMvc.perform(post("/api/nfse-goiania/enviar")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.sucesso").value(false))
                .andExpect(jsonPath("$.mensagemErro").value("XML é obrigatório"));
    }

    @Test
    void testEnviarNfse_ValidacaoCNPJ() throws Exception {
        NFSeGoianiaEnvioRequest request = new NFSeGoianiaEnvioRequest();
        request.setXml("<GerarNfseEnvio><InfRps><IdentificacaoRps><Numero>1</Numero></IdentificacaoRps></InfRps></GerarNfseEnvio>");
        // CNPJ inválido
        request.setPraca(1);
        request.setNumero("1");

        mockMvc.perform(post("/api/nfse-goiania/enviar")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.sucesso").value(false))
                .andExpect(jsonPath("$.mensagemErro").value("CNPJ é obrigatório"));
    }

    @Test
    void testEnviarNfseSimples_ValidacaoParametros() throws Exception {
        String xmlTeste = "<GerarNfseEnvio><InfRps><IdentificacaoRps><Numero>1</Numero></IdentificacaoRps></InfRps></GerarNfseEnvio>";

        mockMvc.perform(post("/api/nfse-goiania/enviar-simples")
                .contentType(MediaType.TEXT_PLAIN)
                .content(xmlTeste)
                .param("cnpj", "12345678000123")
                .param("praca", "1")
                .param("numero", "1"))
                .andExpect(status().isUnprocessableEntity()); // Esperado erro por não ter certificado configurado
    }

    /**
     * Teste com webservice real - só executa se variável de ambiente estiver configurada
     */
    @Test
    @EnabledIfEnvironmentVariable(named = "NFSE_INTEGRATION_TEST", matches = "true")
    void testComunicacaoWebserviceReal() throws Exception {
        // XML de teste válido para Goiânia
        String xmlTeste = """
            <GerarNfseEnvio xmlns="http://nfse.goiania.go.gov.br/xsd/nfse_gyn_v02.xsd">
                <LoteRps Id="lote1">
                    <NumeroLote>1</NumeroLote>
                    <Cnpj>12345678000123</Cnpj>
                    <InscricaoMunicipal>123456</InscricaoMunicipal>
                    <QuantidadeRps>1</QuantidadeRps>
                    <ListaRps>
                        <Rps>
                            <InfRps Id="rps1">
                                <IdentificacaoRps>
                                    <Numero>1</Numero>
                                    <Serie>1</Serie>
                                    <Tipo>1</Tipo>
                                </IdentificacaoRps>
                                <DataEmissao>2025-06-25</DataEmissao>
                                <NaturezaOperacao>1</NaturezaOperacao>
                                <RegimeEspecialTributacao>1</RegimeEspecialTributacao>
                                <OptanteSimplesNacional>1</OptanteSimplesNacional>
                                <IncentivadorCultural>2</IncentivadorCultural>
                                <Status>1</Status>
                                <Servico>
                                    <Valores>
                                        <ValorServicos>100.00</ValorServicos>
                                        <ValorDeducoes>0.00</ValorDeducoes>
                                        <ValorPis>0.00</ValorPis>
                                        <ValorCofins>0.00</ValorCofins>
                                        <ValorInss>0.00</ValorInss>
                                        <ValorIr>0.00</ValorIr>
                                        <ValorCsll>0.00</ValorCsll>
                                        <IssRetido>2</IssRetido>
                                        <ValorIss>5.00</ValorIss>
                                        <Aliquota>0.05</Aliquota>
                                        <DescontoIncondicionado>0.00</DescontoIncondicionado>
                                        <DescontoCondicionado>0.00</DescontoCondicionado>
                                    </Valores>
                                    <ItemListaServico>1401</ItemListaServico>
                                    <Discriminacao>Teste de serviço</Discriminacao>
                                    <CodigoMunicipio>5208707</CodigoMunicipio>
                                </Servico>
                                <Prestador>
                                    <Cnpj>12345678000123</Cnpj>
                                    <InscricaoMunicipal>123456</InscricaoMunicipal>
                                </Prestador>
                                <Tomador>
                                    <IdentificacaoTomador>
                                        <CpfCnpj>
                                            <Cpf>12345678901</Cpf>
                                        </CpfCnpj>
                                    </IdentificacaoTomador>
                                    <RazaoSocial>Tomador Teste</RazaoSocial>
                                </Tomador>
                            </InfRps>
                        </Rps>
                    </ListaRps>
                </LoteRps>
            </GerarNfseEnvio>
            """;

        NFSeGoianiaEnvioRequest request = new NFSeGoianiaEnvioRequest();
        request.setXml(xmlTeste);
        request.setCnpj("12345678000123");
        request.setPraca(1);
        request.setNumero("1");

        // Este teste só funcionará com certificado válido configurado
        mockMvc.perform(post("/api/nfse-goiania/enviar")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.dataHoraProcessamento").exists())
                .andExpect(jsonPath("$.xmlEnviado").exists());
    }

    @Test
    void testConsultarNfse_ValidacaoRequest() throws Exception {
        // Teste básico de validação para consulta
        String xmlConsulta = """
            <ConsultarNfseRpsEnvio xmlns="http://nfse.goiania.go.gov.br/xsd/nfse_gyn_v02.xsd">
                <IdentificacaoRps>
                    <Numero>1</Numero>
                    <Serie>1</Serie>
                    <Tipo>1</Tipo>
                </IdentificacaoRps>
                <Prestador>
                    <Cnpj>12345678000123</Cnpj>
                    <InscricaoMunicipal>123456</InscricaoMunicipal>
                </Prestador>
            </ConsultarNfseRpsEnvio>
            """;

        mockMvc.perform(post("/api/nfse-goiania/consultar-simples")
                .contentType(MediaType.TEXT_PLAIN)
                .content(xmlConsulta)
                .param("cnpj", "12345678000123"))
                .andExpect(status().isUnprocessableEntity()); // Esperado erro por não ter certificado
    }
}
