# API NFSe Goiânia - Comunicação Direta

Esta documentação descreve os endpoints REST modernos para envio e consulta de NFSe de Goiânia, implementados no projeto esocial-novo com **comunicação direta** com o webservice oficial.

## Visão Geral

A nova implementação **elimina completamente** as dependências do `satelliteserver` e `webservice_nfse_goiania`, oferecendo:

- **Comunicação direta** com o webservice oficial de Goiânia (`https://nfse.goiania.go.gov.br/ws/nfse.asmx`)
- **Assinatura digital automática** do XML antes do envio
- **Fluxo completo**: Recebe XML → Assina → Envia → Disponibiliza consulta
- Utiliza Spring Boot e tecnologias atuais
- Recebe XML via body da requisição (não busca dados do banco)
- Oferece endpoints separados para envio e consulta
- Implementa tratamento robusto de erros
- Fornece logging detalhado

## Endpoints Disponíveis

### 1. Envio de NFSe

#### POST `/api/nfse-goiania/enviar`

Envia uma NFSe para o webservice de Goiânia.

**Request Body:**
```json
{
  "xml": "<xml da NFSe>",
  "cnpj": "12345678000123",
  "praca": 1,
  "serie": "1",
  "numero": "123",
  "codCidade": "5208707"
}
```

**Response:**
```json
{
  "xmlRetorno": "<xml de retorno do webservice>",
  "dataHoraProcessamento": "2025-06-25T10:30:00",
  "numeroNfse": "123456",
  "codigoVerificacao": "ABC123",
  "sucesso": true,
  "mensagemErro": null,
  "xmlEnviado": "<xml enviado>",
  "status": "SUCESSO"
}
```

#### POST `/api/nfse-goiania/enviar-simples`

Versão simplificada para compatibilidade com sistemas legados.

**Request Body:** XML da NFSe (text/plain)

**Query Parameters:**
- `cnpj` (obrigatório): CNPJ da empresa
- `praca` (obrigatório): Código da praça
- `numero` (obrigatório): Número da nota
- `serie` (opcional): Série da nota
- `codCidade` (opcional): Código da cidade (padrão: 5208707)

### 2. Consulta de NFSe

#### POST `/api/nfse-goiania/consultar`

Consulta uma NFSe no webservice de Goiânia.

**Request Body:**
```json
{
  "xml": "<xml de consulta>",
  "cnpj": "12345678000123",
  "praca": 1,
  "serie": "1",
  "numero": "123",
  "protocolo": "PROT123"
}
```

**Response:**
```json
{
  "xmlRetorno": "<xml de retorno da consulta>",
  "dataHoraProcessamento": "2025-06-25T10:30:00",
  "sucesso": true,
  "mensagemErro": null,
  "xmlEnviado": "<xml enviado>",
  "status": "CONSULTADO"
}
```

#### POST `/api/nfse-goiania/consultar-simples`

Versão simplificada para consulta.

**Request Body:** XML de consulta (text/plain)

**Query Parameters:**
- `cnpj` (obrigatório): CNPJ da empresa
- `praca` (opcional): Código da praça
- `numero` (opcional): Número da nota
- `protocolo` (opcional): Protocolo de envio

### 3. Health Check

#### GET `/api/nfse-goiania/health`

Verifica se o serviço está funcionando.

**Response:** `NFSe Goiânia Service is running`

## Códigos de Status HTTP

- `200 OK`: Operação realizada com sucesso
- `400 Bad Request`: Dados de entrada inválidos
- `422 Unprocessable Entity`: Erro no processamento da NFSe
- `500 Internal Server Error`: Erro interno do servidor
- `502 Bad Gateway`: Erro na comunicação com o webservice
- `503 Service Unavailable`: Webservice indisponível

## Códigos de Erro

- `NFSE_ERROR`: Erro genérico de NFSe
- `ENVIO_ERROR`: Erro específico no envio
- `CONSULTA_ERROR`: Erro específico na consulta
- `ERRO_CONECTIVIDADE`: Problema de conectividade
- `ERRO_WEBSERVICE`: Erro na comunicação com webservice
- `ERRO_INTERNO`: Erro interno do sistema

## Configuração

### Propriedades da Aplicação

```properties
# URL do webservice oficial de NFSe Goiânia (comunicação direta)
nfse.goiania.url=https://nfse.goiania.go.gov.br/ws/nfse.asmx

# Configurações de timeout para webservice oficial (em milissegundos)
http.client.connect-timeout=45000
http.client.read-timeout=120000

# Configurações de certificado digital (necessário para assinatura)
certificado.empresa.caminho=/path/to/certificado.p12
certificado.empresa.senha=senha_do_certificado
```

### Dependências Necessárias

As seguintes dependências já estão configuradas no `pom.xml`:

- Spring Boot Starter Web
- Apache HttpClient
- Lombok
- Spring Boot Starter Test

## Migração do Sistema Legado

### Antes (Sistema Antigo - Múltiplas Dependências)
```java
// satelliteserver -> webservice_nfse_goiania -> webservice oficial
private void btnNFSEGoianiaActionPerformed(ActionEvent evt) {
    // Busca notas do banco
    emitir = xmlNFSEDao.BuscaNotasEmitir(getFEDERAL(), "5208707");
    // Processa em loop com thread
    xml_retorno = nfseGoiania.Gerar(emitir1.getXML_Envio()); // Via webservice intermediário
}
```

### Depois (Sistema Novo - Comunicação Direta)
```java
// Comunicação direta com webservice oficial de Goiânia
RestTemplate restTemplate = new RestTemplate();
NFSeGoianiaEnvioRequest request = new NFSeGoianiaEnvioRequest();
request.setXml(xmlNfse); // XML já vem pronto
request.setCnpj(cnpj);
// ...

// Fluxo: XML -> Assinatura Digital -> Envio Direto -> Resposta
ResponseEntity<NFSeGoianiaResponse> response = restTemplate.postForEntity(
    "http://localhost:8080/api/nfse-goiania/enviar",
    request,
    NFSeGoianiaResponse.class
);
```

### Vantagens da Nova Arquitetura
- ✅ **Elimina dependências**: Não precisa mais do satelliteserver nem webservice_nfse_goiania
- ✅ **Comunicação direta**: Conecta diretamente com `https://nfse.goiania.go.gov.br/ws/nfse.asmx`
- ✅ **Assinatura automática**: XML é assinado digitalmente automaticamente
- ✅ **Mais rápido**: Menos intermediários = menor latência
- ✅ **Mais confiável**: Menos pontos de falha
- ✅ **Mais simples**: Uma única aplicação Spring Boot

## Exemplos de Uso

### Envio com cURL

```bash
curl -X POST http://localhost:8080/api/nfse-goiania/enviar \
  -H "Content-Type: application/json" \
  -d '{
    "xml": "<GerarNfseEnvio>...</GerarNfseEnvio>",
    "cnpj": "12345678000123",
    "praca": 1,
    "numero": "123"
  }'
```

### Consulta com cURL

```bash
curl -X POST http://localhost:8080/api/nfse-goiania/consultar \
  -H "Content-Type: application/json" \
  -d '{
    "xml": "<ConsultarNfseRpsEnvio>...</ConsultarNfseRpsEnvio>",
    "cnpj": "12345678000123",
    "protocolo": "PROT123"
  }'
```

## Logs

O sistema gera logs detalhados para auditoria e troubleshooting:

```
2025-06-25 10:30:00 INFO  - Iniciando envio de NFSe - CNPJ: 12345678000123, Praça: 1, Número: 123
2025-06-25 10:30:01 INFO  - NFSe enviada com sucesso - Número NFSe: 123456
```

## Monitoramento

- Health check disponível em `/api/nfse-goiania/health`
- Métricas do Spring Boot Actuator (se habilitado)
- Logs estruturados para análise

## Suporte

Para dúvidas ou problemas:
1. Verifique os logs da aplicação
2. Teste o health check
3. Valide a conectividade com o webservice
4. Contate a equipe de desenvolvimento
