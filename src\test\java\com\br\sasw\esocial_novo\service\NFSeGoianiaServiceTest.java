package com.br.sasw.esocial_novo.service;

import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaEnvioRequest;
import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Testes para NFSeGoianiaService
 */
@ExtendWith(MockitoExtension.class)
class NFSeGoianiaServiceTest {

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private NFSeGoianiaService nfseGoianiaService;

    @Test
    void testEnviarNfse_Sucesso() {
        // Arrange
        NFSeGoianiaEnvioRequest request = new NFSeGoianiaEnvioRequest();
        request.setXml("<GerarNfseEnvio><InfRps Id=\"rps1\"><IdentificacaoRps><Numero>1</Numero></IdentificacaoRps></InfRps></GerarNfseEnvio>");
        request.setCnpj("12345678000123");
        request.setPraca(1);
        request.setNumero("1");

        String soapResponse = """
            <?xml version="1.0" encoding="utf-8"?>
            <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                <soap:Body>
                    <GerarNfseResponse xmlns="http://nfse.goiania.go.gov.br/ws/">
                        <GerarNfseResult>
                            <ListaNfse>
                                <CompNfse>
                                    <Nfse>
                                        <InfNfse>
                                            <Numero>123456</Numero>
                                            <CodigoVerificacao>ABC123</CodigoVerificacao>
                                        </InfNfse>
                                    </Nfse>
                                </CompNfse>
                            </ListaNfse>
                        </GerarNfseResult>
                    </GerarNfseResponse>
                </soap:Body>
            </soap:Envelope>
            """;

        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenReturn(new ResponseEntity<>(soapResponse, HttpStatus.OK));

        // Act
        NFSeGoianiaResponse response = nfseGoianiaService.enviarNfse(request);

        // Assert
        assertNotNull(response);
        assertTrue(response.isSucesso());
        assertEquals("123456", response.getNumeroNfse());
        assertEquals("ABC123", response.getCodigoVerificacao());
        assertEquals("SUCESSO", response.getStatus());
        
        // Verifica se o RestTemplate foi chamado
        verify(restTemplate, times(1)).exchange(anyString(), any(), any(), eq(String.class));
    }

    @Test
    void testEnviarNfse_Erro() {
        // Arrange
        NFSeGoianiaEnvioRequest request = new NFSeGoianiaEnvioRequest();
        request.setXml("<GerarNfseEnvio><InfRps><IdentificacaoRps><Numero>1</Numero></IdentificacaoRps></InfRps></GerarNfseEnvio>");
        request.setCnpj("12345678000123");
        request.setPraca(1);
        request.setNumero("1");

        String soapResponseErro = """
            <?xml version="1.0" encoding="utf-8"?>
            <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                <soap:Body>
                    <GerarNfseResponse xmlns="http://nfse.goiania.go.gov.br/ws/">
                        <GerarNfseResult>
                            <ListaMensagemRetorno>
                                <MensagemRetorno>
                                    <Codigo>E001</Codigo>
                                    <Mensagem>Erro na validação do XML</Mensagem>
                                </MensagemRetorno>
                            </ListaMensagemRetorno>
                        </GerarNfseResult>
                    </GerarNfseResponse>
                </soap:Body>
            </soap:Envelope>
            """;

        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenReturn(new ResponseEntity<>(soapResponseErro, HttpStatus.OK));

        // Act
        NFSeGoianiaResponse response = nfseGoianiaService.enviarNfse(request);

        // Assert
        assertNotNull(response);
        assertFalse(response.isSucesso());
        assertEquals("ERRO", response.getStatus());
        assertNotNull(response.getMensagemErro());
    }

    @Test
    void testValidacaoXmlAssinatura() {
        // Arrange
        NFSeGoianiaEnvioRequest request = new NFSeGoianiaEnvioRequest();
        request.setXml("<GerarNfseEnvio><InfRps><IdentificacaoRps><Numero>1</Numero></IdentificacaoRps></InfRps></GerarNfseEnvio>");
        request.setCnpj("12345678000123");

        // Act & Assert
        // Este teste validaria se o XML está sendo assinado corretamente
        // Em um ambiente real, seria necessário configurar certificados de teste
        assertNotNull(request.getXml());
        assertFalse(request.getXml().isEmpty());
    }
}
