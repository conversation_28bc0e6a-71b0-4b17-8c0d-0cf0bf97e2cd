# Configurações específicas para NFSe Goiânia
# Este arquivo pode ser usado como referência para configuração

# URL do webservice de NFSe Goiânia
nfse.goiania.url=http://localhost:8080/webservice_nfse_goiania

# Configurações de timeout HTTP (em milissegundos)
http.client.connect-timeout=30000
http.client.read-timeout=60000

# Configurações de logging específicas para NFSe
logging.level.com.br.sasw.esocial_novo.service.NFSeGoianiaService=DEBUG
logging.level.com.br.sasw.esocial_novo.controller.NFSeGoianiaController=DEBUG

# Configurações do servidor (se necessário ajustar)
server.port=8080
server.servlet.context-path=/

# Configurações de encoding
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# Configurações de CORS (se necessário para frontend)
cors.allowed-origins=*
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
