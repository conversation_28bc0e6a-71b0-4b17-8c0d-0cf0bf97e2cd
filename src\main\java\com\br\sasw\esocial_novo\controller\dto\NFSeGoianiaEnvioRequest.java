package com.br.sasw.esocial_novo.controller.dto;

import lombok.Data;

/**
 * DTO para requisição de envio de NFSe Goiânia
 */
@Data
public class NFSeGoianiaEnvioRequest {
    
    /**
     * XML da NFSe a ser enviada
     */
    private String xml;
    
    /**
     * CNPJ da empresa emissora
     */
    private String cnpj;
    
    /**
     * C<PERSON>digo da praça
     */
    private Integer praca;
    
    /**
     * Série da nota
     */
    private String serie;
    
    /**
     * Número da nota
     */
    private String numero;
    
    /**
     * <PERSON><PERSON><PERSON> da cidade (5208707 para Goiânia)
     */
    private String codCidade;
}
