package com.br.sasw.esocial_novo.exception;

/**
 * Exceção específica para operações de NFSe Goiânia
 */
public class NFSeGoianiaException extends RuntimeException {

    private final String codigo;
    private final String detalhe;

    public NFSeGoianiaException(String message) {
        super(message);
        this.codigo = "NFSE_ERROR";
        this.detalhe = message;
    }

    public NFSeGoianiaException(String message, Throwable cause) {
        super(message, cause);
        this.codigo = "NFSE_ERROR";
        this.detalhe = message;
    }

    public NFSeGoianiaException(String codigo, String message, String detalhe) {
        super(message);
        this.codigo = codigo;
        this.detalhe = detalhe;
    }

    public NFSeGoianiaException(String codigo, String message, String detalhe, Throwable cause) {
        super(message, cause);
        this.codigo = codigo;
        this.detalhe = detalhe;
    }

    public String getCodigo() {
        return codigo;
    }

    public String getDetalhe() {
        return detalhe;
    }
}
