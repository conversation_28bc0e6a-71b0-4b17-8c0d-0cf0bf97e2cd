package com.br.sasw.esocial_novo.controller;

import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaConsultaRequest;
import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaEnvioRequest;
import com.br.sasw.esocial_novo.controller.dto.NFSeGoianiaResponse;
import com.br.sasw.esocial_novo.service.NFSeGoianiaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Controller REST para operações de NFSe Goiânia
 * Comunicação direta com webservice oficial - elimina dependências do satelliteserver
 */
@RestController
@RequestMapping("/api/nfse-goiania")
@RequiredArgsConstructor
@Slf4j
public class NFSeGoianiaController {

    private final NFSeGoianiaService nfseGoianiaService;

    /**
     * Endpoint para envio de NFSe
     * Fluxo: Recebe XML -> Assina digitalmente -> Envia para webservice oficial -> Retorna resultado
     *
     * @param request Dados da NFSe a ser enviada
     * @return Resposta com o resultado do envio
     */
    @PostMapping("/enviar")
    public ResponseEntity<NFSeGoianiaResponse> enviarNfse(@RequestBody NFSeGoianiaEnvioRequest request) {
        log.info("Recebida requisição de envio de NFSe - CNPJ: {}, Praça: {}, Número: {} [COMUNICAÇÃO DIRETA]",
                request.getCnpj(), request.getPraca(), request.getNumero());

        try {
            // Validações básicas
            if (request.getXml() == null || request.getXml().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("XML é obrigatório"));
            }

            if (request.getCnpj() == null || request.getCnpj().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("CNPJ é obrigatório"));
            }

            if (request.getPraca() == null) {
                return ResponseEntity.badRequest().body(createErrorResponse("Praça é obrigatória"));
            }

            if (request.getNumero() == null || request.getNumero().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("Número é obrigatório"));
            }

            // Define código da cidade padrão para Goiânia se não informado
            if (request.getCodCidade() == null || request.getCodCidade().trim().isEmpty()) {
                request.setCodCidade("5208707");
            }

            NFSeGoianiaResponse response = nfseGoianiaService.enviarNfse(request);

            if (response.isSucesso()) {
                log.info("NFSe enviada com sucesso - CNPJ: {}, Número NFSe: {}",
                        request.getCnpj(), response.getNumeroNfse());
                return ResponseEntity.ok(response);
            } else {
                log.warn("Erro no envio de NFSe - CNPJ: {}, Erro: {}",
                        request.getCnpj(), response.getMensagemErro());
                return ResponseEntity.unprocessableEntity().body(response);
            }

        } catch (Exception e) {
            log.error("Erro inesperado ao processar envio de NFSe", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("Erro interno do servidor: " + e.getMessage()));
        }
    }

    /**
     * Endpoint para consulta de NFSe
     * Comunicação direta com webservice oficial de Goiânia
     *
     * @param request Dados para consulta da NFSe
     * @return Resposta com o resultado da consulta
     */
    @PostMapping("/consultar")
    public ResponseEntity<NFSeGoianiaResponse> consultarNfse(@RequestBody NFSeGoianiaConsultaRequest request) {
        log.info("Recebida requisição de consulta de NFSe - CNPJ: {}, Praça: {}, Número: {} [COMUNICAÇÃO DIRETA]",
                request.getCnpj(), request.getPraca(), request.getNumero());

        try {
            // Validações básicas
            if (request.getXml() == null || request.getXml().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("XML é obrigatório"));
            }

            if (request.getCnpj() == null || request.getCnpj().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("CNPJ é obrigatório"));
            }

            NFSeGoianiaResponse response = nfseGoianiaService.consultarNfse(request);
            
            if (response.isSucesso()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.unprocessableEntity().body(response);
            }

        } catch (Exception e) {
            log.error("Erro inesperado ao processar consulta de NFSe", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("Erro interno do servidor: " + e.getMessage()));
        }
    }

    /**
     * Endpoint simplificado para envio apenas com XML
     * Mantém compatibilidade com sistemas legados
     */
    @PostMapping("/enviar-simples")
    public ResponseEntity<NFSeGoianiaResponse> enviarNfseSimples(
            @RequestBody String xml,
            @RequestParam String cnpj,
            @RequestParam Integer praca,
            @RequestParam String numero,
            @RequestParam(required = false) String serie,
            @RequestParam(required = false, defaultValue = "5208707") String codCidade) {
        
        log.info("Recebida requisição simplificada de envio de NFSe - CNPJ: {}, Praça: {}, Número: {}", 
                cnpj, praca, numero);

        NFSeGoianiaEnvioRequest request = new NFSeGoianiaEnvioRequest();
        request.setXml(xml);
        request.setCnpj(cnpj);
        request.setPraca(praca);
        request.setNumero(numero);
        request.setSerie(serie);
        request.setCodCidade(codCidade);

        return enviarNfse(request);
    }

    /**
     * Endpoint simplificado para consulta apenas com XML
     * Mantém compatibilidade com sistemas legados
     */
    @PostMapping("/consultar-simples")
    public ResponseEntity<NFSeGoianiaResponse> consultarNfseSimples(
            @RequestBody String xml,
            @RequestParam String cnpj,
            @RequestParam(required = false) Integer praca,
            @RequestParam(required = false) String numero,
            @RequestParam(required = false) String protocolo) {
        
        log.info("Recebida requisição simplificada de consulta de NFSe - CNPJ: {}, Protocolo: {}", 
                cnpj, protocolo);

        NFSeGoianiaConsultaRequest request = new NFSeGoianiaConsultaRequest();
        request.setXml(xml);
        request.setCnpj(cnpj);
        request.setPraca(praca);
        request.setNumero(numero);
        request.setProtocolo(protocolo);

        return consultarNfse(request);
    }

    /**
     * Endpoint de health check
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("NFSe Goiânia Service is running");
    }

    /**
     * Cria uma resposta de erro padronizada
     */
    private NFSeGoianiaResponse createErrorResponse(String mensagem) {
        NFSeGoianiaResponse response = new NFSeGoianiaResponse();
        response.setSucesso(false);
        response.setStatus("ERRO");
        response.setMensagemErro(mensagem);
        response.setDataHoraProcessamento(java.time.LocalDateTime.now());
        return response;
    }
}
